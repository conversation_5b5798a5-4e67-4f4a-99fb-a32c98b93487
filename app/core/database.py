"""
Database configuration and connection management.
Supports PostgreSQL with SQLAlchemy 2.0 async and MongoDB with Motor.
"""

from typing import AsyncGenerator, Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

from app.core.config import settings


class Base(DeclarativeBase):
    """Base class for SQLAlchemy models."""
    pass


# PostgreSQL Database Engine
engine = create_async_engine(
    str(settings.DATABASE_URL),
    echo=settings.DEBUG,
    future=True,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function that yields database sessions.
    
    Yields:
        AsyncSession: Database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# MongoDB Client
class MongoDB:
    """MongoDB connection manager."""
    
    client: Optional[AsyncIOMotorClient] = None
    database: Optional[AsyncIOMotorDatabase] = None


mongodb = MongoDB()


async def connect_to_mongo():
    """Create database connection."""
    mongodb.client = AsyncIOMotorClient(settings.MONGODB_URL)
    mongodb.database = mongodb.client[settings.MONGODB_DB_NAME]


async def close_mongo_connection():
    """Close database connection."""
    if mongodb.client:
        mongodb.client.close()


async def get_mongodb() -> AsyncIOMotorDatabase:
    """
    Dependency function that yields MongoDB database.

    Returns:
        AsyncIOMotorDatabase: MongoDB database instance
    """
    if mongodb.database is None:
        raise RuntimeError("MongoDB is not connected")
    return mongodb.database


# Redis Connection
try:
    import redis.asyncio as redis
    from redis.asyncio import Redis
except ImportError:
    # Fallback for older redis versions
    import redis
    from redis import Redis

redis_client: Optional[Redis] = None


async def connect_to_redis():
    """Create Redis connection."""
    global redis_client
    redis_client = redis.from_url(
        settings.REDIS_URL,
        encoding="utf-8",
        decode_responses=True,
        health_check_interval=30,
    )


async def close_redis_connection():
    """Close Redis connection."""
    global redis_client
    if redis_client:
        redis_client.close()
        redis_client = None

async def get_redis() -> Redis:
    """
    Dependency function that yields Redis client.
    
    Returns:
        Redis: Redis client instance
    """
    if redis_client is None:
        raise RuntimeError("Redis is not connected")
    return redis_client


# Database initialization
async def init_db():
    """Initialize database tables."""
    # Import all models to ensure they're registered with SQLAlchemy
    from app.models import user, role, credential, tag, workflow  # noqa: F401

    async with engine.begin() as conn:
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)


async def init_mongodb():
    """Initialize MongoDB collections and indexes."""
    if mongodb.database is None:
        raise RuntimeError("MongoDB is not connected")

    try:
        # Initialize event type collection indexes
        from app.repositories.event_type_repository import EventTypeRepository
        event_type_repo = EventTypeRepository(mongodb.database)
        await event_type_repo.ensure_indexes()

    except Exception as e:
        from app.utils.logging import get_logger
        logger = get_logger("database.mongodb")
        logger.error(f"Failed to initialize MongoDB: {e}")
        raise


async def create_initial_data():
    """Create initial data like superuser and default roles."""
    from app.services.user_service import UserService
    from app.schemas.user import UserCreate

    async with AsyncSessionLocal() as session:
        try:
            user_service = UserService(session)

            # Check if superuser exists
            superuser = await user_service.get_by_email(settings.FIRST_SUPERUSER_EMAIL)
            if not superuser:
                # Create superuser
                superuser_data = UserCreate(
                    email=settings.FIRST_SUPERUSER_EMAIL,
                    password=settings.FIRST_SUPERUSER_PASSWORD,
                    full_name="System Administrator",
                    is_superuser=True,
                    is_active=True,
                    avatar_url=None,
                    bio=None,
                )
                await user_service.create(superuser_data)

            await session.commit()
        except Exception:
            await session.rollback()
            raise