"""
Business logic service for dynamic schema management.
"""

from typing import Dict, List, Optional, Any
from uuid import UUID
import asyncio

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.repositories.schema_repository import SchemaDefinitionRepository
# Import SchemaValidationService inside methods where it's needed, not at module level
from app.schemas.schema import (
    SchemaDefinitionCreate, SchemaDefinitionUpdate, SchemaDefinition, SchemaDefinitionList, SchemaField, 
    FieldType, ValidationRule, SchemaValidationResponse, SchemaValidationError
)
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

logger = get_logger("services.schema")

# Define audit field names as a constant at the module level
AUDIT_FIELD_NAMES = {"created_by", "edited_by", "created_on", "edited_on"}


class SchemaService:
    """Service for schema management with caching and validation."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize service with database.
        
        Args:
            db: MongoDB database instance
        """
        self.repository = SchemaDefinitionRepository(db)
        self._schema_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """Initialize the service and create default schemas."""
        await self.repository.ensure_indexes()
        await self._create_default_contact_schema()
    
    async def create_schema_definition(self, schema_data: SchemaDefinitionCreate) -> SchemaDefinition:
        """
        Create a new schema and corresponding MongoDB collection.
        
        Args:
            schema_data: Schema Definition creation data
            
        Returns:
            Schema: Created schema
            
        Raises:
            ValidationError: If schema validation fails
            ConflictError: If schema name already exists
        """
        # Import here to avoid circular imports
        from app.services.schema_validation_service import SchemaValidationService
        
        # Validate schema definition
        validation_errors = SchemaValidationService.validate_schema_definition(schema_data.fields)
        if validation_errors:
            raise ValidationError(f"Schema validation failed: {'; '.join(validation_errors)}")
        
        # Check if schema name already exists
        existing = await self.repository.get_by_name(schema_data.name)
        if existing:
            raise ConflictError(f"Schema with name '{schema_data.name}' already exists")
        
        # Add default audit fields
        audit_fields = self._get_default_audit_fields()
        
        # Check for field name conflicts
        existing_field_names = {field.name for field in schema_data.fields}
        audit_field_names = {field.name for field in audit_fields}
        
        # Only add audit fields that don't already exist
        new_audit_fields = [field for field in audit_fields 
                            if field.name not in existing_field_names]
        
        # Append the new audit fields
        schema_data.fields.extend(new_audit_fields)
        
        # Create schema
        created_doc = await self.repository.create(schema_data)
        
        # Create corresponding MongoDB collection with validation
        await self._create_collection_for_schema(schema_data)
        
        # Update cache
        async with self._cache_lock:
            self._schema_cache[schema_data.name] = created_doc
        
        # Convert to response model
        return self._doc_to_schema(created_doc)
    
    async def _create_collection_for_schema(self, schema_data: SchemaDefinitionCreate) -> None:
        """
        Create a MongoDB collection for the schema with validation rules.
        
        Args:
            schema_data: Schema creation data
        """
        collection_name = schema_data.name
        db = self.repository.db
        
        # Check if collection already exists
        existing_collections = await db.list_collection_names()
        if collection_name in existing_collections:
            logger.warning(f"Collection '{collection_name}' already exists")
            return
        
        # Create collection
        await db.create_collection(collection_name)
        logger.info(f"Created collection '{collection_name}'")
        
        # Build JSON Schema validation from fields
        json_schema = self._build_json_schema_from_fields(schema_data.fields)
        
        # Apply validation schema to collection
        validator = {
            "$jsonSchema": json_schema
        }
        
        await db.command({
            "collMod": collection_name,
            "validator": validator,
            "validationLevel": "moderate"  # allows existing documents to remain valid
        })
        
        logger.info(f"Applied schema validation to collection '{collection_name}'")

    def _build_json_schema_from_fields(self, fields: List[SchemaField]) -> Dict[str, Any]:
        """
        Build MongoDB JSON Schema from schema fields.
        
        Args:
            fields: Schema field definitions
            
        Returns:
            Dict: MongoDB JSON Schema
        """
        properties = {}
        required = []
        
        for field in fields:
            # Add to required list if field is required
            if field.validation_rules.required:
                required.append(field.name)
            
            # Build property definition based on field type
            property_def = {
                "description": field.description or field.display_name
            }
            
            # Map field types to MongoDB types
            if field.field_type == FieldType.STRING or field.field_type == FieldType.EMAIL or \
               field.field_type == FieldType.PHONE or field.field_type == FieldType.URL:
                property_def["bsonType"] = "string"
                
                # Add string validations
                if field.validation_rules.min_length is not None:
                    property_def["minLength"] = field.validation_rules.min_length
                if field.validation_rules.max_length is not None:
                    property_def["maxLength"] = field.validation_rules.max_length
                if field.validation_rules.pattern:
                    property_def["pattern"] = field.validation_rules.pattern
                if field.validation_rules.enum_values:
                    property_def["enum"] = field.validation_rules.enum_values
                    
            elif field.field_type == FieldType.NUMBER:
                property_def["bsonType"] = "double"
                if field.validation_rules.min_value is not None:
                    property_def["minimum"] = field.validation_rules.min_value
                if field.validation_rules.max_value is not None:
                    property_def["maximum"] = field.validation_rules.max_value
                
            elif field.field_type == FieldType.INTEGER:
                property_def["bsonType"] = "int"
                if field.validation_rules.min_value is not None:
                    property_def["minimum"] = field.validation_rules.min_value
                if field.validation_rules.max_value is not None:
                    property_def["maximum"] = field.validation_rules.max_value
                
            elif field.field_type == FieldType.BOOLEAN:
                property_def["bsonType"] = "bool"
            
            elif field.field_type == FieldType.DATE or field.field_type == FieldType.DATETIME:
                # For date/datetime fields, allow both date and string formats
                property_def["bsonType"] = ["date", "string"]
                
            elif field.field_type == FieldType.OBJECT:
                property_def["bsonType"] = "object"
                
            elif field.field_type == FieldType.ARRAY:
                property_def["bsonType"] = "array"
        
            properties[field.name] = property_def
    
        # Add special handling for audit fields
        audit_field_names = {"created_by", "edited_by", "created_on", "edited_on"}
        
        return {
            "bsonType": "object",
            "required": required,
            "properties": properties,
            "additionalProperties": False  # Restrict to only defined properties
        }
    
    async def get_schema_by_id(self, schema_id: str) -> SchemaDefinition:
        """
        Get schema Definition by ID.
        
        Args:
            schema_id: Schema ID
            
        Returns:
            Schema: Schema instance
            
        Raises:
            NotFoundError: If schema not found
        """
        doc = await self.repository.get_by_id(schema_id)
        if not doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found")
        
        return self._doc_to_schema(doc)
    
    async def get_schema_by_name(self, name: str, use_cache: bool = True) -> SchemaDefinition:
        """
        Get schema Definition by name.
        
        Args:
            name: Schema name
            use_cache: Whether to use cache
            
        Returns:
            Schema: Schema instance
            
        Raises:
            NotFoundError: If schema not found
        """
        # Check cache first
        if use_cache:
            async with self._cache_lock:
                if name in self._schema_cache:
                    return self._doc_to_schema(self._schema_cache[name])
        
        # Get from database
        doc = await self.repository.get_by_name(name)
        if not doc:
            raise NotFoundError(f"Schema with name '{name}' not found")
        
        # Update cache
        if use_cache:
            async with self._cache_lock:
                self._schema_cache[name] = doc
        
        return self._doc_to_schema(doc)
    
    async def get_schemas(
        self,
        skip: int = 0,
        limit: int = 100,
        created_by: Optional[int] = None,
        include_system: bool = True
    ) -> SchemaDefinitionList:
        """
        Get multiple schemas with pagination.
        
        Args:
            skip: Number of schemas to skip
            limit: Maximum number of schemas to return
            created_by: Filter by creator
            include_system: Whether to include system schemas
            
        Returns:
            SchemaList: List of schemas with pagination info
        """
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if not include_system:
            filters["is_system_schema"] = False
        
        # Get schemas and count
        docs = await self.repository.get_multi(skip=skip, limit=limit, filters=filters)
        total = await self.repository.count(filters=filters)
        
        # Convert to response models
        schemas = [self._doc_to_schema(doc) for doc in docs]
        
        return SchemaDefinitionList(
            schemas=schemas,
            total=total,
            page=(skip // limit) + 1,
            page_size=limit
        )
    
    async def update_schema(self, schema_id: str, update_data: SchemaDefinitionUpdate) -> SchemaDefinition:
        """
        Update an existing schema Definition.
        
        Args:
            schema_id: Schema ID
            update_data: Update data
            
        Returns:
            Schema: Updated schema
            
        Raises:
            NotFoundError: If schema not found
            ValidationError: If update validation fails
        """
        # Get existing schema
        existing_doc = await self.repository.get_by_id(schema_id)
        if not existing_doc:
            raise NotFoundError(f"Schema Definition with ID '{schema_id}' not found")
        
        # Check if it's a system schema
        print("existing_doc", existing_doc)
        print("existing_doc.get(is_system_schema", existing_doc.get("is_system_schema", False))
        if existing_doc.get("is_system_schema", False):
            # For system schemas, only allow adding new fields, not modifying existing ones
            if update_data.fields:
                await self._validate_system_schema_update(existing_doc, update_data.fields)
        
        if update_data.fields is not None:
            # Validate field definitions
            validation_errors = SchemaValidationService.validate_schema_definition(update_data.fields)
            if validation_errors:
                raise ValidationError(f"Schema validation failed: {'; '.join(validation_errors)}")
            
            # Ensure audit fields are preserved
            existing_fields = existing_doc.get("fields", [])
            existing_audit_fields = [field for field in existing_fields 
                                    if field.get("name") in AUDIT_FIELD_NAMES]
            
            # Convert to SchemaField objects if they're in dict form
            existing_audit_field_objects = []
            for field in existing_audit_fields:
                if isinstance(field, dict):
                    field_obj = SchemaField(**field)
                    existing_audit_field_objects.append(field_obj)
                else:
                    existing_audit_field_objects.append(field)
            
            # Get new field names
            new_field_names = {field.name for field in update_data.fields}
            
            # Only add audit fields that don't exist in the update
            preserved_audit_fields = [field for field in existing_audit_field_objects 
                                    if field.name not in new_field_names]
            
            # Append preserved audit fields to the update
            update_data.fields.extend(preserved_audit_fields)
        
        # Update schema
        updated_doc = await self.repository.update(schema_id, update_data)
        if not updated_doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found or could not be updated")
        
        # If fields were updated, update the collection validation schema
        if update_data.fields is not None:
            collection_name = updated_doc["name"]
            
            # Rebuild JSON Schema from updated fields
            json_schema = self._build_json_schema_from_fields(update_data.fields)
            print("json_schema55555", json_schema)   
            
            # Apply updated validation to collection
            validator = {"$jsonSchema": json_schema}
            try:
                await self.repository.db.command({
                    "collMod": collection_name,
                    "validator": validator,
                    "validationLevel": "moderate"
                })
                logger.info(f"Updated schema validation for collection '{collection_name}'")
            except Exception as e:
                logger.error(f"Failed to update collection validation: {e}")
                # Consider whether to fail the entire operation or just log the error
    
        # Update cache
        async with self._cache_lock:
            schema_name = updated_doc["name"]
            self._schema_cache[schema_name] = updated_doc
        
        return self._doc_to_schema(updated_doc)
    
    async def delete_schema(self, schema_id: str) -> bool:
        """
        Delete a schema Definition.
        
        Args:
            schema_id: Schema ID
            
        Returns:
            bool: True if deleted
            
        Raises:
            NotFoundError: If schema not found
            ValidationError: If schema cannot be deleted
        """
        # Get existing schema
        existing_doc = await self.repository.get_by_id(schema_id)
        if not existing_doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found")
        
        # Check if it's a system schema
        if existing_doc.get("is_system_schema", False):
            raise ValidationError("System schemas cannot be deleted")
        
        # Delete schema
        deleted = await self.repository.delete(schema_id)
        
        # Remove from cache
        if deleted:
            async with self._cache_lock:
                schema_name = existing_doc["name"]
                self._schema_cache.pop(schema_name, None)
        
        return deleted
    
    async def validate_schema_definition(self, fields: List[SchemaField]) -> SchemaValidationResponse:
        """
        Validate schema field definitions.
        
        Args:
            fields: Schema fields to validate
            
        Returns:
            SchemaValidationResponse: Validation result
        """
        validation_errors = SchemaValidationService.validate_schema_definition(fields)
        
        errors = [
            SchemaValidationError(field="schema", message=error, invalid_value=None)
            for error in validation_errors
        ]
        
        return SchemaValidationResponse(
            valid=len(errors) == 0,
            errors=errors
        )
    
    async def get_schema_fields(self, schema_name: str) -> List[SchemaField]:
        """
        Get schema fields by name.
        
        Args:
            schema_name: Schema name
            
        Returns:
            List[SchemaField]: Schema fields
            
        Raises:
            NotFoundError: If schema not found
        """
        schema = await self.get_schema_by_name(schema_name)
        return schema.fields
    
    async def clear_cache(self) -> None:
        """Clear the schema cache."""
        async with self._cache_lock:
            self._schema_cache.clear()
    
    async def _create_default_contact_schema(self) -> None:
        """Create the default Contact schema if it doesn't exist."""
        try:
            # Check if Contact schema already exists
            existing = await self.repository.get_by_name("contact")
            if existing:
                logger.info("Contact schema already exists")
                return
            
            # Define Contact schema fields
            contact_fields = [
                {
                    "name": "id",
                    "display_name": "ID",
                    "description": "Unique identifier (auto-generated)",
                    "field_type": "string",
                    "validation_rules": {
                        "required": False,
                        "unique": True,
                        "min_length": None,
                        "max_length": None,
                        "min_value": None,
                        "max_value": None,
                        "pattern": None,
                        "enum_values": None
                    },
                    "default_value": None,
                    "is_default_field": True,
                    "order": 0
                },
                {
                    "name": "first_name",
                    "display_name": "First Name",
                    "description": "Contact's first name",
                    "field_type": "string",
                    "validation_rules": {
                        "required": True,
                        "unique": False,
                        "min_length": 1,
                        "max_length": 100,
                        "min_value": None,
                        "max_value": None,
                        "pattern": None,
                        "enum_values": None
                    },
                    "default_value": None,
                    "is_default_field": True,
                    "order": 1
                },
                {
                    "name": "last_name",
                    "display_name": "Last Name",
                    "description": "Contact's last name",
                    "field_type": "string",
                    "validation_rules": {
                        "required": True,
                        "unique": False,
                        "min_length": 1,
                        "max_length": 100,
                        "min_value": None,
                        "max_value": None,
                        "pattern": None,
                        "enum_values": None
                    },
                    "default_value": None,
                    "is_default_field": True,
                    "order": 2
                },
                {
                    "name": "email",
                    "display_name": "Email",
                    "description": "Contact's email address",
                    "field_type": "email",
                    "validation_rules": {
                        "required": True,
                        "unique": True,
                        "min_length": None,
                        "max_length": None,
                        "min_value": None,
                        "max_value": None,
                        "pattern": None,
                        "enum_values": None
                    },
                    "default_value": None,
                    "is_default_field": True,
                    "order": 3
                },
                {
                    "name": "phone",
                    "display_name": "Phone",
                    "description": "Contact's phone number",
                    "field_type": "phone",
                    "validation_rules": {
                        "required": True,
                        "unique": False,
                        "min_length": None,
                        "max_length": None,
                        "min_value": None,
                        "max_value": None,
                        "pattern": None,
                        "enum_values": None
                    },
                    "default_value": None,
                    "is_default_field": True,
                    "order": 4
                }
            ]
            
            # Create system schema document
            schema_doc = {
                "name": "contact",
                "display_name": "Contact",
                "description": "Default contact schema with mandatory fields",
                "fields": contact_fields,
                "version": 1,
                "created_by": None,  # System created
                "updated_by": None,
            }
            
            # Create the schema
            created_doc = await self.repository.create_system_schema(schema_doc)
            
            # Update cache
            async with self._cache_lock:
                self._schema_cache["contact"] = created_doc
            
            logger.info("Created default Contact schema")
            
        except Exception as e:
            logger.error(f"Failed to create default Contact schema: {e}")
            # Don't raise exception as this shouldn't prevent app startup
    
    async def _validate_system_schema_update(self, existing_doc: Dict[str, Any], new_fields: List[SchemaField]) -> None:
        """
        Validate updates to system schemas.
        
        Args:
            existing_doc: Existing schema document
            new_fields: New field definitions
            
        Raises:
            ValidationError: If update is not allowed
        """
        existing_fields = {field["name"]: field for field in existing_doc["fields"]}
        
        for field in new_fields:
            if field.name in existing_fields:
                existing_field = existing_fields[field.name]
                
                # Check if it's a default field
                if existing_field.get("is_default_field", False):
                    # Only allow changes to non-critical properties
                    if (field.field_type.value != existing_field["field_type"] or
                        field.validation_rules.required != existing_field["validation_rules"]["required"]):
                        raise ValidationError(
                            f"Cannot modify type or required status of default field '{field.name}'"
                        )
    
    def _doc_to_schema(self, doc: Dict[str, Any]) -> SchemaDefinition:
        """
        Convert database document to Schema model.
        
        Args:
            doc: Database document
            
        Returns:
            Schema: Schema model
        """
        # Convert fields
        fields = []
        for field_doc in doc["fields"]:
            validation_rules = ValidationRule(**field_doc["validation_rules"])
            field = SchemaField(
                name=field_doc["name"],
                display_name=field_doc["display_name"],
                description=field_doc.get("description"),
                field_type=FieldType(field_doc["field_type"]),
                validation_rules=validation_rules,
                default_value=field_doc.get("default_value"),
                is_default_field=field_doc.get("is_default_field", False),
                order=field_doc.get("order", 0)
            )
            fields.append(field)
        
        return SchemaDefinition(
            id=doc["id"],
            name=doc["name"],
            display_name=doc["display_name"],
            description=doc.get("description"),
            fields=fields,
            version=doc["version"],
            created_by=doc.get("created_by"),
            updated_by=doc.get("updated_by"),
            is_system_schema=doc.get("is_system_schema", False),
            created_at=doc["created_at"],
            updated_at=doc["updated_at"]
        )
    
    def _get_default_audit_fields(self) -> List[SchemaField]:
        """
        Generate standard audit fields that should be added to all schemas.
        
        Returns:
            List[SchemaField]: List of audit fields
        """
        audit_fields = [
            SchemaField(
                name="created_by",
                display_name="Created By",
                description="User ID who created the record",
                field_type=FieldType.INTEGER,
                validation_rules=ValidationRule(required=False),
                default_value=None,
                is_default_field=True,
                order=900
            ),
            SchemaField(
                name="edited_by",
                display_name="Edited By",
                description="User ID who last modified the record",
                field_type=FieldType.INTEGER,
                validation_rules=ValidationRule(required=False),
                default_value=None,
                is_default_field=True,
                order=901
            ),
            SchemaField(
                name="created_on",
                display_name="Created On",
                description="Timestamp when the record was created",
                field_type=FieldType.DATETIME,
                validation_rules=ValidationRule(required=False),
                default_value=None,
                is_default_field=True,
                order=902
            ),
            SchemaField(
                name="edited_on",
                display_name="Edited On",
                description="Timestamp when the record was last modified",
                field_type=FieldType.DATETIME,
                validation_rules=ValidationRule(required=False),
                default_value=None,
                is_default_field=True,
                order=903
            )
        ]
        
        return audit_fields
